
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Target, BarChart3, Users, Globe, Eye } from 'lucide-react';

const AngelVeins = () => {
  const handleContactUs = () => {
    // Scroll to contact section
    const contactSection = document.querySelector('footer');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-gradient-to-r from-[#40826D] via-[#4a9d7a] to-[#40826D] shadow-lg sticky top-0 z-50 relative overflow-hidden">
        {/* Crystal glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(255,255,255,0.1)_0%,transparent_70%)]"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 flex items-center justify-center">
                <img
                  src="/veridian-logo.png"
                  alt="Veridian Vista Logo"
                  className="w-10 h-10 object-contain drop-shadow-lg"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  Veridian Vista
                </h1>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              <a href="/" className="text-white/80 hover:text-white font-medium">Home</a>
              <a href="/founders-lode" className="text-white/80 hover:text-white font-medium">Founder</a>
              <a href="/angel-veins" className="text-white/80 hover:text-white font-medium">Investor</a>
              <a href="/about" className="text-white/80 hover:text-white font-medium">About Us</a>
            </nav>

            <Button
              className="bg-white text-[#40826D] hover:bg-gray-100 font-medium px-6 py-2"
              onClick={handleContactUs}
            >
              Contact Us
            </Button>
          </div>
        </div>
      </header>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Angel Veins - Investor Discovery</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover and connect with quality investors worldwide through our AI-powered platform.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">For Entrepreneurs</h2>
            <p className="text-gray-600 mb-4">
              Finding the right investors is crucial for your startup's success. Angel Veins helps you:
            </p>
            <ul className="space-y-2 text-gray-600">
              <li>• Identify investors who match your industry and stage</li>
              <li>• Access detailed investor profiles and preferences</li>
              <li>• Understand investment patterns and success rates</li>
              <li>• Connect with warm introductions when possible</li>
            </ul>
          </div>

          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">For Investors</h2>
            <p className="text-gray-600 mb-4">
              Join our curated network of angel investors and venture capitalists:
            </p>
            <ul className="space-y-2 text-gray-600">
              <li>• Showcase your investment focus and portfolio</li>
              <li>• Receive qualified deal flow from our platform</li>
              <li>• Connect with other investors for co-investment</li>
              <li>• Access market intelligence and trends</li>
            </ul>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <Card className="text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <Target className="w-12 h-12 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D]">Precision Matching</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">AI-powered matching between startups and investors based on compatibility.</p>
            </CardContent>
          </Card>

          <Card className="text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <BarChart3 className="w-12 h-12 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D]">Investment Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Comprehensive data on investment trends, success rates, and market insights.</p>
            </CardContent>
          </Card>

          <Card className="text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <Users className="w-12 h-12 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D]">Verified Network</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Access to a curated network of verified angel investors and VCs.</p>
            </CardContent>
          </Card>

          <Card className="text-center bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader>
              <Globe className="w-12 h-12 text-[#40826D] mx-auto mb-4" />
              <CardTitle className="text-[#40826D]">Global Reach</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Connect with investors across 50+ countries and emerging markets.</p>
            </CardContent>
          </Card>
        </div>

        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Ready to Connect?</h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Join thousands of entrepreneurs and investors who are already using Angel Veins to make meaningful connections.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-[#40826D] hover:bg-[#40826D]/90 text-white font-semibold"
            >
              Get Started
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-[#40826D] text-[#40826D] hover:bg-[#40826D]/10 font-semibold"
            >
              Learn More
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AngelVeins;
