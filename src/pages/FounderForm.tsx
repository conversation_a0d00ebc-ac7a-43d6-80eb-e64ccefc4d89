import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  User, 
  Mail, 
  Linkedin, 
  Building, 
  Globe, 
  Target, 
  DollarSign, 
  MapPin, 
  TrendingUp,
  MessageSquare,
  CheckCircle,
  Lightbulb
} from 'lucide-react';
import { toast } from '@/components/ui/sonner';

interface FounderFormData {
  founder_name: string;
  founder_email: string;
  founder_linkedin_url: string;
  company_name: string;
  company_website_url: string;
  company_stage: string;
  industry: string;
  location: string;
  funding_stage: string;
  funding_amount_needed: string;
  company_description: string;
  problem_statement: string;
  solution_description: string;
  target_market: string;
  business_model: string;
  traction_metrics: string;
  team_size: string;
  previous_funding: string;
  source: string;
}

const FounderForm = () => {
  const [formData, setFormData] = useState<FounderFormData>({
    founder_name: '',
    founder_email: '',
    founder_linkedin_url: '',
    company_name: '',
    company_website_url: '',
    company_stage: '',
    industry: '',
    location: '',
    funding_stage: '',
    funding_amount_needed: '',
    company_description: '',
    problem_statement: '',
    solution_description: '',
    target_market: '',
    business_model: '',
    traction_metrics: '',
    team_size: '',
    previous_funding: '',
    source: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const companyStages = [
    'Idea Stage',
    'MVP Development',
    'Beta Testing',
    'Product Launch',
    'Early Traction',
    'Growth Stage',
    'Scaling',
    'Mature'
  ];

  const industries = [
    'AI/ML',
    'Fintech',
    'Healthcare',
    'Biotech',
    'SaaS',
    'E-commerce',
    'Edtech',
    'Proptech',
    'Cleantech',
    'Mobility',
    'Gaming',
    'Consumer',
    'Enterprise',
    'Hardware',
    'Blockchain',
    'Cybersecurity',
    'Other'
  ];

  const fundingStages = [
    'Pre-seed',
    'Seed',
    'Series A',
    'Series B',
    'Series C',
    'Series D+',
    'Growth',
    'Bridge Round'
  ];

  const fundingAmounts = [
    'Under $100K',
    '$100K - $500K',
    '$500K - $1M',
    '$1M - $5M',
    '$5M - $10M',
    '$10M - $25M',
    '$25M - $50M',
    '$50M+',
    'Not seeking funding currently'
  ];

  const teamSizes = [
    '1 (Solo founder)',
    '2-3',
    '4-10',
    '11-25',
    '26-50',
    '51-100',
    '100+'
  ];

  const handleInputChange = (field: keyof FounderFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Here you would typically send the data to your backend/NocoDB
      console.log('Founder form submitted:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Registration successful! Welcome to our founder network.');
      
      // Reset form
      setFormData({
        founder_name: '',
        founder_email: '',
        founder_linkedin_url: '',
        company_name: '',
        company_website_url: '',
        company_stage: '',
        industry: '',
        location: '',
        funding_stage: '',
        funding_amount_needed: '',
        company_description: '',
        problem_statement: '',
        solution_description: '',
        target_market: '',
        business_model: '',
        traction_metrics: '',
        team_size: '',
        previous_funding: '',
        source: '',
      });
      
    } catch (error) {
      toast.error('Something went wrong. Please try again.');
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-teal-50">
      {/* Header */}
      <header className="bg-gradient-to-r from-[#40826D] to-[#2D5A4A] shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
                <Lightbulb className="w-6 h-6 text-[#40826D]" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">Veridian Vista</h1>
                <p className="text-white/80 text-sm">Founder Registration</p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              <a href="/" className="text-white/80 hover:text-white font-medium">Home</a>
              <a href="/founder" className="text-white hover:text-white font-medium">Founder</a>
              <a href="/investor" className="text-white/80 hover:text-white font-medium">Investor</a>
              <a href="/about" className="text-white/80 hover:text-white font-medium">About Us</a>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Join Our Founder Network</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Connect with investors and fellow entrepreneurs. Share your vision and get discovered by the right partners.
          </p>
        </div>

        <Card className="bg-white shadow-xl border-0">
          <CardHeader className="bg-gradient-to-r from-[#40826D] to-[#2D5A4A] text-white rounded-t-lg">
            <CardTitle className="text-2xl flex items-center space-x-2">
              <Lightbulb className="w-6 h-6" />
              <span>Founder Profile</span>
            </CardTitle>
          </CardHeader>

          <CardContent className="p-8">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Personal Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <User className="w-5 h-5 text-[#40826D]" />
                  <span>Personal Information</span>
                </h3>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="founder_name" className="text-gray-700 font-medium">
                      Full Name *
                    </Label>
                    <Input
                      id="founder_name"
                      value={formData.founder_name}
                      onChange={(e) => handleInputChange('founder_name', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="founder_email" className="text-gray-700 font-medium flex items-center space-x-1">
                      <Mail className="w-4 h-4" />
                      <span>Email Address *</span>
                    </Label>
                    <Input
                      id="founder_email"
                      type="email"
                      value={formData.founder_email}
                      onChange={(e) => handleInputChange('founder_email', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="founder_linkedin_url" className="text-gray-700 font-medium flex items-center space-x-1">
                      <Linkedin className="w-4 h-4" />
                      <span>LinkedIn Profile</span>
                    </Label>
                    <Input
                      id="founder_linkedin_url"
                      type="url"
                      value={formData.founder_linkedin_url}
                      onChange={(e) => handleInputChange('founder_linkedin_url', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="https://linkedin.com/in/yourprofile"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="location" className="text-gray-700 font-medium flex items-center space-x-1">
                      <MapPin className="w-4 h-4" />
                      <span>Location</span>
                    </Label>
                    <Input
                      id="location"
                      value={formData.location}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="City, Country"
                    />
                  </div>
                </div>
              </div>

              {/* Company Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <Building className="w-5 h-5 text-[#40826D]" />
                  <span>Company Information</span>
                </h3>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="company_name" className="text-gray-700 font-medium">
                      Company Name *
                    </Label>
                    <Input
                      id="company_name"
                      value={formData.company_name}
                      onChange={(e) => handleInputChange('company_name', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company_website_url" className="text-gray-700 font-medium flex items-center space-x-1">
                      <Globe className="w-4 h-4" />
                      <span>Company Website</span>
                    </Label>
                    <Input
                      id="company_website_url"
                      type="url"
                      value={formData.company_website_url}
                      onChange={(e) => handleInputChange('company_website_url', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="https://yourcompany.com"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company_stage" className="text-gray-700 font-medium">
                      Company Stage *
                    </Label>
                    <Select value={formData.company_stage} onValueChange={(value) => handleInputChange('company_stage', value)}>
                      <SelectTrigger className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]">
                        <SelectValue placeholder="Select company stage" />
                      </SelectTrigger>
                      <SelectContent>
                        {companyStages.map((stage) => (
                          <SelectItem key={stage} value={stage}>
                            {stage}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="industry" className="text-gray-700 font-medium">
                      Industry *
                    </Label>
                    <Select value={formData.industry} onValueChange={(value) => handleInputChange('industry', value)}>
                      <SelectTrigger className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]">
                        <SelectValue placeholder="Select industry" />
                      </SelectTrigger>
                      <SelectContent>
                        {industries.map((industry) => (
                          <SelectItem key={industry} value={industry}>
                            {industry}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="team_size" className="text-gray-700 font-medium">
                      Team Size
                    </Label>
                    <Select value={formData.team_size} onValueChange={(value) => handleInputChange('team_size', value)}>
                      <SelectTrigger className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]">
                        <SelectValue placeholder="Select team size" />
                      </SelectTrigger>
                      <SelectContent>
                        {teamSizes.map((size) => (
                          <SelectItem key={size} value={size}>
                            {size}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="company_description" className="text-gray-700 font-medium">
                    Company Description *
                  </Label>
                  <Textarea
                    id="company_description"
                    value={formData.company_description}
                    onChange={(e) => handleInputChange('company_description', e.target.value)}
                    className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                    placeholder="Describe your company in 2-3 sentences..."
                    rows={3}
                    required
                  />
                </div>
              </div>

              {/* Business Details */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <Target className="w-5 h-5 text-[#40826D]" />
                  <span>Business Details</span>
                </h3>

                <div className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="problem_statement" className="text-gray-700 font-medium">
                      Problem Statement
                    </Label>
                    <Textarea
                      id="problem_statement"
                      value={formData.problem_statement}
                      onChange={(e) => handleInputChange('problem_statement', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="What problem are you solving?"
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="solution_description" className="text-gray-700 font-medium">
                      Solution Description
                    </Label>
                    <Textarea
                      id="solution_description"
                      value={formData.solution_description}
                      onChange={(e) => handleInputChange('solution_description', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="How does your solution address the problem?"
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="target_market" className="text-gray-700 font-medium">
                      Target Market
                    </Label>
                    <Textarea
                      id="target_market"
                      value={formData.target_market}
                      onChange={(e) => handleInputChange('target_market', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="Who are your target customers?"
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="business_model" className="text-gray-700 font-medium">
                      Business Model
                    </Label>
                    <Textarea
                      id="business_model"
                      value={formData.business_model}
                      onChange={(e) => handleInputChange('business_model', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="How do you make money?"
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="traction_metrics" className="text-gray-700 font-medium">
                      Traction & Metrics
                    </Label>
                    <Textarea
                      id="traction_metrics"
                      value={formData.traction_metrics}
                      onChange={(e) => handleInputChange('traction_metrics', e.target.value)}
                      className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                      placeholder="Share key metrics, milestones, or traction achieved..."
                      rows={3}
                    />
                  </div>
                </div>
              </div>

              {/* Funding Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <DollarSign className="w-5 h-5 text-[#40826D]" />
                  <span>Funding Information</span>
                </h3>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="funding_stage" className="text-gray-700 font-medium">
                      Current Funding Stage
                    </Label>
                    <Select value={formData.funding_stage} onValueChange={(value) => handleInputChange('funding_stage', value)}>
                      <SelectTrigger className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]">
                        <SelectValue placeholder="Select funding stage" />
                      </SelectTrigger>
                      <SelectContent>
                        {fundingStages.map((stage) => (
                          <SelectItem key={stage} value={stage}>
                            {stage}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="funding_amount_needed" className="text-gray-700 font-medium">
                      Funding Amount Needed
                    </Label>
                    <Select value={formData.funding_amount_needed} onValueChange={(value) => handleInputChange('funding_amount_needed', value)}>
                      <SelectTrigger className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]">
                        <SelectValue placeholder="Select funding amount" />
                      </SelectTrigger>
                      <SelectContent>
                        {fundingAmounts.map((amount) => (
                          <SelectItem key={amount} value={amount}>
                            {amount}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="previous_funding" className="text-gray-700 font-medium">
                    Previous Funding History
                  </Label>
                  <Textarea
                    id="previous_funding"
                    value={formData.previous_funding}
                    onChange={(e) => handleInputChange('previous_funding', e.target.value)}
                    className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                    placeholder="List any previous funding rounds, amounts, and investors..."
                    rows={3}
                  />
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5 text-[#40826D]" />
                  <span>Additional Information</span>
                </h3>

                <div className="space-y-2">
                  <Label htmlFor="source" className="text-gray-700 font-medium">
                    How did you hear about us?
                  </Label>
                  <Input
                    id="source"
                    value={formData.source}
                    onChange={(e) => handleInputChange('source', e.target.value)}
                    className="border-gray-300 focus:border-[#40826D] focus:ring-[#40826D]"
                    placeholder="e.g., LinkedIn, referral, conference, etc."
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-center pt-8">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-gradient-to-r from-[#40826D] to-[#2D5A4A] hover:from-[#2D5A4A] hover:to-[#40826D] text-white px-12 py-3 text-lg font-semibold"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-5 h-5 mr-2" />
                      Join Founder Network
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default FounderForm;
