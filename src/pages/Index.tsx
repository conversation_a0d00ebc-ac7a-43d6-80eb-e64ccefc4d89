
import React, { useState } from 'react';
import Header from '@/components/Header';
import ProductDemo from '@/components/ProductDemo';
import { Button } from '@/components/ui/button';

import { Mail, Phone, MapPin, Github, Twitter, Linkedin, Star, TrendingUp, Shield, Brain, BarChart3, Users, Eye, Lightbulb, Target } from 'lucide-react';

const Index = () => {
  const handleStartDiscovering = () => {
    // Scroll to contact section
    const contactSection = document.querySelector('footer');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-gradient-to-r from-[#40826D] via-[#4a9d7a] to-[#40826D] shadow-lg sticky top-0 z-50 relative overflow-hidden">
        {/* Crystal glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(255,255,255,0.1)_0%,transparent_70%)]"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 flex items-center justify-center">
                <img
                  src="/veridian-logo.png"
                  alt="Veridian Vista Logo"
                  className="w-10 h-10 object-contain drop-shadow-lg"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">
                  Veridian Vista
                </h1>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-8">
              <a href="/" className="text-white/80 hover:text-white font-medium">Home</a>
              <a href="/founders-lode" className="text-white/80 hover:text-white font-medium">Founder</a>
              <a href="/angel-veins" className="text-white/80 hover:text-white font-medium">Investor</a>
              <a href="/about" className="text-white/80 hover:text-white font-medium">About Us</a>
            </nav>

            <Button
              className="bg-white text-[#40826D] hover:bg-gray-100 font-medium px-6 py-2"
              onClick={handleStartDiscovering}
            >
              Contact Us
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section - Extended Height */}
      <section className="min-h-screen flex items-center py-32 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto w-full">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div className="space-y-12">
              <div className="space-y-8">
                <h1 className="text-6xl md:text-7xl font-bold text-gray-900 leading-tight">
                  Be the smartest
                  <br />
                  <span className="text-[#40826D]">in the room.</span>
                </h1>

                <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                  AI-powered investment intelligence that helps you discover breakthrough companies before they become obvious to everyone else.
                </p>

                {/* Value Propositions */}
                <div className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-[#40826D] rounded-full flex items-center justify-center">
                      <Eye className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-xl text-gray-700 font-medium">Know who to back</span>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-[#40826D] rounded-full flex items-center justify-center">
                      <Target className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-xl text-gray-700 font-medium">Make the right decision faster</span>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-[#40826D] rounded-full flex items-center justify-center">
                      <TrendingUp className="w-5 h-5 text-white" />
                    </div>
                    <span className="text-xl text-gray-700 font-medium">Maximize the value of your investments</span>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-[#40826D] to-[#008C8C] hover:from-[#40826D]/90 hover:to-[#008C8C]/90 text-white px-10 py-5 text-xl font-semibold rounded-lg shadow-lg"
                  onClick={handleStartDiscovering}
                >
                  Get Started
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-[#40826D] text-[#40826D] hover:bg-[#40826D]/10 px-10 py-5 text-xl font-semibold rounded-lg"
                  onClick={handleStartDiscovering}
                >
                  Learn More
                </Button>
              </div>
            </div>

            {/* Right Content - Dashboard Preview */}
            <div className="relative">
              <div className="relative">
                <img
                  src="/lovable-uploads/aca6c8ae-cb9d-4581-9fb5-ac2c4c13184b.png"
                  alt="Investment Dashboard Interface"
                  className="w-full h-auto rounded-2xl shadow-2xl"
                />
                {/* Floating elements for visual interest */}
                <div className="absolute -top-4 -right-4 w-20 h-20 bg-[#40826D]/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-[#008C8C]/10 rounded-full blur-xl"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              AI-Powered Investment Intelligence
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Advanced technology that transforms how you discover and evaluate early-stage opportunities
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {/* Feature Card 1 */}
            <div className="group bg-white p-8 rounded-xl border border-gray-200 hover:border-[#40826D]/30 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#40826D] to-[#008C8C] rounded-xl flex items-center justify-center mb-6 group-hover:scale-105 transition-transform duration-300">
                <Brain className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">AI Founder Analysis</h3>
              <p className="text-gray-600 leading-relaxed">
                Smart evaluation of entrepreneur backgrounds, track records, and potential using advanced machine learning algorithms
              </p>
            </div>

            {/* Feature Card 2 */}
            <div className="group bg-white p-8 rounded-xl border border-gray-200 hover:border-[#40826D]/30 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#40826D] to-[#008C8C] rounded-xl flex items-center justify-center mb-6 group-hover:scale-105 transition-transform duration-300">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Market Intelligence</h3>
              <p className="text-gray-600 leading-relaxed">
                Deep insights into emerging sectors, market trends, and competitive landscapes before they become mainstream
              </p>
            </div>

            {/* Feature Card 3 */}
            <div className="group bg-white p-8 rounded-xl border border-gray-200 hover:border-[#40826D]/30 hover:shadow-lg transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-[#40826D] to-[#008C8C] rounded-xl flex items-center justify-center mb-6 group-hover:scale-105 transition-transform duration-300">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Risk Assessment</h3>
              <p className="text-gray-600 leading-relaxed">
                Predictive analytics and risk modeling to support confident investment decision-making at the earliest stages
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Clean Footer */}
      <footer className="bg-[#40826D] py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 flex items-center justify-center">
                  <img src="/veridian-logo.png" alt="Logo" className="w-8 h-8 object-contain drop-shadow-lg" />
                </div>
                <h3 className="text-2xl font-bold text-white">Veridian Vista</h3>
              </div>
              <p className="text-white/80 mb-6 max-w-md">
                AI-Powered Early Investment Discovery Platform - connecting sophisticated investors with tomorrow's breakthrough companies.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <Twitter className="w-6 h-6" />
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <Linkedin className="w-6 h-6" />
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <Github className="w-6 h-6" />
                </a>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-4">Product</h4>
              <ul className="space-y-3">
                <li>
                  <a href="/angel-veins" className="text-white/80 hover:text-white transition-colors">
                    Angel Veins
                  </a>
                </li>
                <li>
                  <a href="/founders-lode" className="text-white/80 hover:text-white transition-colors">
                    Founder's Lode
                  </a>
                </li>
                <li>
                  <a href="/about" className="text-white/80 hover:text-white transition-colors">
                    About Us
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-4">Contact</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Mail className="w-4 h-4 text-white" />
                  <span className="text-white/80"><EMAIL></span>
                </div>
                {/* <div className="flex items-center space-x-3">
                  <Phone className="w-4 h-4 text-[#40826D]" />
                  <span className="text-gray-600">+****************</span>
                </div>
                <div className="flex items-center space-x-3">
                  <MapPin className="w-4 h-4 text-[#40826D]" />
                  <span className="text-gray-600">Palo Alto, CA</span>
                </div> */}
              </div>
            </div>
          </div>

          <div className="border-t border-white/20 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-white/60 text-sm mb-4 md:mb-0">
                © 2025 Veridian Vista. All rights reserved.
              </p>
              <div className="flex space-x-6 text-sm">
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  Privacy Policy
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  Terms of Service
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  Security
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
